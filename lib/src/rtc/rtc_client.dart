import 'dart:async';

import 'package:mediasfu_mediasoup_client/mediasfu_mediasoup_client.dart'
    as msp;

import 'rtc_signal.dart';
import 'rtc_signal.model.dart';
import 'rtc_consume.observer.dart';
import 'rtc_produce.observer.dart';
import 'rtc_producer.dart';
import 'rtc_consumer.dart';
import 'rtc_log.observer.dart';

/// RTC编解码器枚举
/// RTC编解码器枚举
enum RtcCodec {
  /// VP8视频编码器
  /// MIME类型: video/vp8
  vp8('video/vp8'),

  /// VP9视频编码器
  /// MIME类型: video/vp9
  vp9('video/vp9'),

  /// H.264视频编码器
  /// MIME类型: video/h264
  h264('video/h264'),
  // h265('video/h265'),

  /// Opus音频编码器
  /// MIME类型: audio/opus
  opus('audio/opus');

  /// MIME类型字符串
  final String mimeType;

  /// 构造函数
  const RtcCodec(this.mimeType);
}

/// RTC客户端观察者接口
abstract interface class RtcClientObserver
    implements RtcLogObserver, RtcProduceObserver, RtcConsumeObserver {}

/// RTC客户端实现类
class RtcClient implements RtcEventHandler {
  /// 信令通道
  final RtcSignal _signal;

  /// 观察者弱引用
  final WeakReference<RtcClientObserver> _observer;

  /// mediasoup设备实例
  late msp.Device _device;

  /// 发送传输通道
  msp.Transport? _sendTransport;

  /// 接收传输通道
  msp.Transport? _recvTransport;

  /// 异步操作完成器映射
  final _completers = <String, Completer?>{};

  /// 媒体处理器映射
  final _mediaHandlers = <String, RtcMediaHandler>{};

  /// 构造函数
  RtcClient(this._signal, this._observer) {
    _signal.lock(WeakReference(this));
  }

  /// 释放资源
  Future<void> dispose() async {
    await leaveRoom();
    _signal.dispose();
  }

  /// 加入房间
  Future<String> joinRoom() async {
    await _signal.joinRoom();
    final routerRtpCapabilities = await _signal.getRouterRtpCapabilities();
    _device = msp.Device();
    await _device.load(routerRtpCapabilities: routerRtpCapabilities);

    // 如果是广播者角色且发送通道未创建，则创建发送通道
    if (_sendTransport == null &&
        RtcPeerRole.broadcaster == _signal.peerSelf.role) {
      final transportInfo = await _signal.createWebRtcTransport(true, false);
      _sendTransport = _device.createSendTransportFromMap(
        transportInfo,
        producerCallback: (msp.Producer producer) {
          final rtcProducer = RtcProducer(
            producer,
            _signal,
            WeakReference(this),
          );
          _mediaHandlers[producer.source] = rtcProducer;
          _completers[producer.source]?.complete(rtcProducer);
        },
      );
      _listenSendTransport();
    }

    // 如果接收通道未创建，则创建接收通道
    if (_recvTransport == null) {
      final transportInfo = await _signal.createWebRtcTransport(false, true);
      _recvTransport = _device.createRecvTransportFromMap(
        transportInfo,
        consumerCallback: (msp.Consumer consumer, dynamic aaa) {
          final rtcConsumer = RtcConsumer(
            consumer,
            _signal,
            WeakReference(this),
          );
          _mediaHandlers[consumer.id] = rtcConsumer;
          _completers[consumer.id]?.complete(rtcConsumer);
        },
      );
      _listenRecvTransport();
      await _signal.consume(_recvTransport!.id, _device.rtpCapabilities);
    }

    // 注意: 可能需要等待1秒，否则 produce 会报错(transport内部初始化并未完成)
    // await Future.delayed(Duration(seconds: 1));
    return _signal.roomId;
  }

  /// 离开房间
  Future<void> leaveRoom() async {
    if (_signal.isInRoom) {
      await _signal.leaveRoom();
      _mediaHandlers.forEach((key, value) async => await value.close());
      _mediaHandlers.clear();
      _completers.clear();
      _recvTransport?.close();
      _sendTransport?.close();
    }
  }

  /// 创建新的生产者
  Future<RtcProducer> newProducer({
    required String identifier,
    required msp.MediaStreamTrack track,
    required msp.MediaStream stream,
    RtcCodec? rtcCodec,
    List<msp.RtpEncodingParameters>? encodings,
    msp.ProducerCodecOptions? codecOptions,
  }) async {
    assert(
      _sendTransport != null,
      'Send transport is null, please join room first',
    );
    if (_completers.containsKey(identifier)) {
      throw Exception('Producer $identifier already exists');
    }
    final completer = Completer<RtcProducer>();
    final canProduce = _device.canProduce(
      msp.typeStringToRTCRtpMediaType[track.kind]!,
    );
    if (!canProduce) {
      throw Exception('Device not support produce');
    }

    // 设置视频或音频的编码参数
    if ('video' == track.kind) {
      rtcCodec ??= RtcCodec.vp8;
      encodings ??= [
        msp.RtpEncodingParameters(
          scaleResolutionDownBy: 1,
          maxBitrate: 4 * 1024 * 1024,
          scalabilityMode: 'L1T3',
          dtx: true,
        )
      ];
    } else {
      rtcCodec ??= RtcCodec.opus;
      encodings ??= [];
    }

    // 查找对应的编解码器
    final codec = _device.rtpCapabilities.codecs.firstWhere(
      (codec) =>
          codec.mimeType.toLowerCase() == rtcCodec!.mimeType.toLowerCase(),
    );

    // 创建生产者
    _sendTransport!.produce(
      track: track,
      stream: stream,
      source: identifier,
      codec: codec,
      encodings: encodings,
      codecOptions: codecOptions ??
          msp.ProducerCodecOptions(
            opusDtx: 1,
            opusFec: 1,
            opusPtime: 10,
            videoGoogleStartBitrate: 1000,
            videoGoogleMaxBitrate: 4 * 1024 * 1024,
          ),
    );
    _completers[identifier] = completer;
    return completer.future;
  }

  /// 创建新的消费者
  Future<RtcConsumer> newConsumer(RtcStreamOption streamOption) async {
    assert(
      _recvTransport != null,
      'Recv transport is null, please join room first',
    );
    if (_completers.containsKey(streamOption.id)) {
      throw Exception('Consumer ${streamOption.id} already exists');
    }
    final completer = Completer<RtcConsumer>();
    _recvTransport!.consume(
      id: streamOption.id,
      producerId: streamOption.producerId,
      peerId: streamOption.producerPeerId,
      kind: streamOption.kind,
      rtpParameters: streamOption.rtpParameters,
    );
    _completers[streamOption.id] = completer;
    return completer.future;
  }

  Future<List<msp.StatsReport>> getState() async {
    return await _sendTransport?.getState() ?? [];
  }

  /// 清除媒体源
  @override
  void clearSource(String source) {
    _mediaHandlers.remove(source);
    _completers.remove(source);
  }

  /// 监听发送传输通道事件
  void _listenSendTransport() {
    // 连接事件
    _sendTransport!.on('connect', (data) async {
      Function callback = data['callback'];
      Function errback = data['errback'];
      msp.DtlsParameters dtlsParameters = data['dtlsParameters'];
      try {
        await _signal.connectWebRtcTransport(
          _sendTransport!.id,
          dtlsParameters,
        );
        callback();
      } catch (error) {
        errback(error);
      }
    });

    // 生产事件
    _sendTransport!.on('produce', (data) async {
      Function callback = data['callback'];
      Function errback = data['errback'];
      String kind = data['kind'];
      msp.RtpParameters rtpParameters = data['rtpParameters'];
      try {
        final producerId = await _signal.produce(
          _sendTransport!.id,
          MediaKind.fromString(kind),
          rtpParameters,
        );
        callback(producerId);
      } catch (error) {
        errback(error);
      }
    });
    // _sendTransport!.on('icegatheringstatechange', (data) {
    //   print("icegatheringstatechange >>>>>>>>>>>>>>>>>>>>>>>>> $data");
    // });
    // _sendTransport!.on('icecandidateerror', (data) {
    //   print("icecandidateerror >>>>>>>>>>>>>>>>>>>>>>>>> $data");
    // });
    _sendTransport!.on('connectionstatechange', (data) async {
      final connectionState = data['connectionState'];
      _observer.target?.rtcDebug(
        "RtcClient",
        'Send transport connectionstatechange: $connectionState',
      );
    });

    // 关闭事件
    _sendTransport!.on('close', () {
      _observer.target?.rtcDebug(
        "RtcClient",
        'Send transport close',
      );
    });
  }

  /// 监听接收传输通道事件
  void _listenRecvTransport() {
    // 连接事件
    _recvTransport!.on('connect', (data) async {
      Function callback = data['callback'];
      Function errback = data['errback'];
      msp.DtlsParameters dtlsParameters = data['dtlsParameters'];
      try {
        await _signal.connectWebRtcTransport(
          _recvTransport!.id,
          dtlsParameters,
        );
        callback();
      } catch (error) {
        errback(error);
      }
    });

    // 连接状态变化事件
    _recvTransport!.on('connectionstatechange', (data) async {
      final connectionState = data['connectionState'];
      _observer.target?.rtcDebug(
        "RtcClient",
        'Recv transport connectionstatechange: $connectionState',
      );
    });

    // 关闭事件
    _recvTransport!.on('close', () {
      _observer.target?.rtcDebug(
        "RtcClient",
        'Recv transport close',
      );
    });
  }
}
