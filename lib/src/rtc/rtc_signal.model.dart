import 'package:mediasfu_mediasoup_client/mediasfu_mediasoup_client.dart'
    as msp;

/// RTC对等端角色枚举
enum RtcPeerRole {
  /// 观众角色
  audience,

  /// 广播者角色
  broadcaster;

  /// 从字符串创建RtcPeerRole枚举
  /// [role] 角色字符串
  factory RtcPeerRole.fromString(String role) {
    return RtcPeerRole.values.firstWhere(
      (e) => e.name == role.toLowerCase(),
    );
  }
}

/// RTC对等端类
class RtcPeer {
  /// 对等端唯一标识
  final String id;

  /// 对等端角色
  final RtcPeerRole role;

  /// 对等端名称
  final String? name;

  /// 对等端头像
  final String? avatar;

  /// 构造函数
  /// [id] 对等端ID
  /// [role] 对等端角色
  /// [name] 对等端名称(可选)
  /// [avatar] 对等端头像(可选)
  RtcPeer(this.id, this.role, [this.name, this.avatar]);

  /// 从Map创建RtcPeer对象
  /// [map] 包含对等端信息的Map
  factory RtcPeer.fromMap(Map<String, dynamic> map) {
    return RtcPeer(
      map['id'],
      RtcPeerRole.fromString(map['role']),
      map['name'],
      map['avatar'],
    );
  }

  /// 将RtcPeer对象转换为Map
  Map<String, dynamic> toMap() {
    return {
      "id": id,
      "role": role.name,
      "name": name,
      "avatar": avatar,
    };
  }

  @override
  String toString() {
    return toMap().toString();
  }
}

/// RTC消费者类型枚举
enum RtcConsumerType {
  /// 简单类型
  simple,

  /// 联播类型
  simulcast,

  /// 可扩展视频编码
  svc,

  /// 管道类型
  pipe;

  /// 从字符串创建RtcConsumerType枚举
  /// [type] 类型字符串
  factory RtcConsumerType.fromString(String type) {
    return RtcConsumerType.values.firstWhere(
      (e) => e.name == type,
    );
  }
}

/// RTC流选项类
class RtcStreamOption {
  /// 流ID
  final String id;

  /// 生产者对等端ID
  final String producerPeerId;

  /// 生产者ID
  final String producerId;

  /// 媒体类型
  final msp.RTCRtpMediaType kind;

  /// RTP参数
  final msp.RtpParameters rtpParameters;

  /// 消费者类型
  final RtcConsumerType type;

  /// 生产者是否暂停
  final bool producerPaused;

  /// 构造函数
  /// [id] 流ID
  /// [producerPeerId] 生产者对等端ID
  /// [producerId] 生产者ID
  /// [kind] 媒体类型
  /// [rtpParameters] RTP参数
  /// [type] 消费者类型
  /// [producerPaused] 生产者是否暂停
  RtcStreamOption(
    this.id,
    this.producerPeerId,
    this.producerId,
    this.kind,
    this.rtpParameters,
    this.type,
    this.producerPaused,
  );

  /// 从Map创建RtcStreamOption对象
  /// [map] 包含流选项信息的Map
  factory RtcStreamOption.fromMap(Map<String, dynamic> map) {
    return RtcStreamOption(
      map['id'],
      map['producerPeerId'],
      map['producerId'],
      msp.typeStringToRTCRtpMediaType[map['kind']]!,
      msp.RtpParameters.fromMap(map['rtpParameters']),
      RtcConsumerType.fromString(map['type']),
      map['producerPaused'],
    );
  }

  /// 将RtcStreamOption对象转换为Map
  Map<String, dynamic> toMap() {
    return {
      "id": id,
      "producerPeerId": producerPeerId,
      "producerId": producerId,
      "kind": msp.typeRTCRtpMediaTypetoString[kind],
      "rtpParameters": rtpParameters.toMap(),
      "type": type.name,
      "producerPaused": producerPaused,
    };
  }

  @override
  String toString() {
    return toMap().toString();
  }
}
