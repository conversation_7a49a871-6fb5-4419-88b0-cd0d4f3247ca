import 'package:mediasfu_mediasoup_client/mediasfu_mediasoup_client.dart'
    as msp;

import 'rtc_signal.dart';

/// RTC生产者类，实现RtcMediaHandler接口
/// 负责管理和控制媒体流的生产
class RtcProducer implements RtcMediaHandler {
  /// mediasoup生产者实例
  final msp.Producer _producer;

  /// RTC信令实例，用于与服务器通信
  final RtcSignal _signal;

  /// RTC事件处理器的弱引用，避免循环引用
  final WeakReference<RtcEventHandler> _rtcEventHandlerRef;

  /// 构造函数
  /// @param producer mediasoup生产者实例
  /// @param signal RTC信令实例
  /// @param rtcEventHandlerRef RTC事件处理器的弱引用
  RtcProducer(
    this._producer,
    this._signal,
    this._rtcEventHandlerRef,
  );

  @override
  msp.MediaStreamTrack get track => _producer.track;

  @override
  Future<void> resume() async {
    await _signal.resumeProducer(_producer.id);
    _producer.resume();
  }

  @override
  Future<void> pause() async {
    await _signal.pauseProducer(_producer.id);
    _producer.pause();
  }

  @override
  Future<void> close() async {
    await _signal.closeProducer(_producer.id);
    _producer.close();
    _rtcEventHandlerRef.target?.clearSource(_producer.source);
  }
}
