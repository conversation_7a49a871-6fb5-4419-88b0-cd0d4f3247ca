import 'dart:async';

import 'package:app_foundation/app_foundation.dart';
import 'package:flutter/scheduler.dart';
import 'package:mediasfu_mediasoup_client/mediasfu_mediasoup_client.dart'
    as msp;
import 'package:withai_rtc_client/src/rtc/rtc_client.dart';
import 'package:withai_rtc_client/src/rtc/rtc_consumer.dart';
import 'package:withai_rtc_client/src/rtc/rtc_producer.dart';
import 'package:withai_rtc_client/src/rtc/rtc_signal.dart';
import 'package:withai_rtc_client/src/rtc/rtc_signal.model.dart';
import 'package:withai_rtc_client/src/rtc/rtc_socketio.signal.dart';

/// 所属模块: home
///
/// 首页
class HomeController extends AppController
    with StateMixin<ApiModel>
    implements RtcClientObserver, RtcSocketioSignalObserver {
  HomeController(super.key, super.routerState);

  final videoRenderer = msp.RTCVideoRenderer();

  late final msp.MediaStream stream;

  late final RtcClient rtcClient;

  RtcProducer? videoProducer;

  RtcProducer? audioProducer;

  RtcConsumer? videoConsumer;

  Timer? timer;

  @override
  void onInit() {
    app.logI("init");
    initRtc();
  }

  @override
  void onReady() {}

  @override
  void onClose() {
    rtcClient.dispose();
  }

  void initRtc() async {
    final devices = await msp.mediaDevices.enumerateDevices();
    for (var device in devices) {
      print("${device.deviceId} ${device.kind}: ${device.label}");
    }
    final deviceId =
        devices.firstWhere((e) => e.label == "00 Pro Capture AIO 4K+").deviceId;
    // final deviceId = devices
    //     .firstWhere((e) => e.label == "USB Capture HDMI: USB Capture H")
    //     .deviceId;

    stream = await msp.mediaDevices.getUserMedia({
      'audio': true,
      'video': {
        'deviceId': deviceId,
        'width': {'ideal': 1920, 'min': 1920, 'max': 1920},
        'height': {'ideal': 1080, 'min': 1080, 'max': 1080},
        'frameRate': {'ideal': 30, 'min': 30},
      }
    });

    // 先初始化渲染器，再获取媒体流
    await videoRenderer.initialize();
    videoRenderer.addListener(() {
      print(">>>2");
    });

    videoRenderer.srcObject = stream;
    print(">>>1");
    app.logI("摄像头初始化成功: ${stream.getVideoTracks().length} 个视频轨道");

    update(LoadState.success(ApiModel()));

    // timer = Timer.periodic(Duration(seconds: 1), (timer) async {
    //   final videoTrack = stream.getVideoTracks().firstOrNull;
    //   final cc = await videoTrack?.captureFrame();
    //   final audioTrack = stream.getAudioTracks().firstOrNull;
    //   print(
    //       ">>>>>>>>>>>>>>>>>>>>>>>>>>>>> ${cc?.lengthInBytes} ${videoTrack?.getSettings()} ${audioTrack?.getSettings()}");
    // });

    rtcClient = RtcClient(
      RtcSocketioSignal(
        "https://10.10.10.111:3000",
        "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJwciI6ImJyb2FkY2FzdGVyIiwicmkiOiIxazNwSk9LUSIsImFpIjoic3VyZ3NtYXJ0IiwicGkiOiI1NTUiLCJpYXQiOjE3NDcwMTcwMDMsInBuIjoi6Lev5Lq655SyIiwicGEiOiIifQ.B_PsMjixdrYWV8GdoaxR34rHGMtZW868BnqHTMTCuQc",
        // "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJwciI6ImJyb2FkY2FzdGVyIiwicmkiOiJLWU8wd1BSeCIsImFpIjoic3VyZ3NtYXJ0IiwicGkiOiIyMyIsImlhdCI6MTc0OTE5OTIxOC40MTY3MTksImV4cCI6MTc0OTI0MjQxOC40MTY3MTkyLCJwbiI6IiIsInBhIjoiIn0.efJkBr6l3qok_MdyH73v0Xs5NpOVXhCW-Qup4pDERCY",
        // "https://192.168.31.108:3000",
        // "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJwciI6ImJyb2FkY2FzdGVyIiwicmkiOiIxazNwSk9LUSIsImFpIjoic3VyZ3NtYXJ0IiwicGkiOiI5OTk5IiwiaWF0IjoxNzQ3MDU2MDc4LjUxNDU1NSwicG4iOiIiLCJwYSI6IiJ9.H3XvDJl3345HhjssUacL22YxpWHDJ9GyZVlsxYgUEJA",
        WeakReference(this),
      ),
      WeakReference(this),
    );
  }

  void joinRoom() async {
    try {
      await rtcClient.joinRoom();
      app.logI("加入房间成功");
    } catch (error) {
      app.logE(error);
    }
  }

  void leaveRoom() async {
    try {
      await rtcClient.leaveRoom();
      app.logI("离开房间成功");
    } catch (error) {
      app.logE(error);
    }
  }

  void produceVideo() async {
    videoProducer = await rtcClient.newProducer(
      identifier: "Front camera",
      rtcCodec: RtcCodec.h264,
      track: stream.getVideoTracks().firstWhere(
            (track) => track.kind == 'video',
          ),
      stream: stream,
    );
  }

  void produceAudio() async {
    audioProducer = await rtcClient.newProducer(
      identifier: "Mic",
      track: stream.getAudioTracks().firstWhere(
            (track) => track.kind == 'audio',
          ),
      stream: stream,
    );
  }

  Map<dynamic, dynamic>? info;

  void getState() {
    if (timer != null) {
      return;
    }
    timer = Timer.periodic(
      Duration(seconds: 1),
      (timer) async {
        final stats = await rtcClient.getState();
        final newInfo = stats
            .firstWhere((element) => element.type == 'outbound-rtp')
            .values;
        if (info != null) {
          int bitrate = newInfo["bytesSent"] - info!["bytesSent"];
          app.logW("bitrate: $bitrate");
        }
        info = newInfo;
      },
    );
  }

  @override
  void rtcDebug(String source, dynamic message) {
    app.logD("[$source]: ${{"content": message}}");
  }

  @override
  void rtcError(String source, dynamic message) {
    app.logE("[$source]: $message");
  }

  @override
  void rtcSignalException(dynamic message) {
    app.logE(message);
  }

  msp.RTCVideoRenderer? remoteAudioRender;

  msp.MediaStream? remoteAudioStream;

  RtcConsumer? audioConsumer;

  @override
  void rtcSignalNewConsumer(RtcStreamOption streamOption) async {
    print(">>>>>>>>>>>>>>>>>> ${streamOption.kind}");
    if (streamOption.kind == msp.RTCRtpMediaType.RTCRtpMediaTypeAudio) {
      if (remoteAudioRender == null) {
        remoteAudioRender = msp.RTCVideoRenderer();
        await remoteAudioRender!.initialize();
        remoteAudioRender!.srcObject = remoteAudioStream;
      }
      audioConsumer = await rtcClient.newConsumer(streamOption);
      audioConsumer?.resume();
    }
  }

  @override
  void rtcSignalStatusChanged(RtcSignalStatus status) {
    app.logW(status);
    if (RtcSignalStatus.connected == status) {
      update(LoadState.success(ApiModel()));
    }
  }

  @override
  void rtcSignalPeerJoined(RtcPeer peer) {
    app.logI("peer joined: ${peer.id} ${peer.name}");
  }

  @override
  void rtcSignalPeerLeft(RtcPeer peer) {
    app.logI("peer left: ${peer.id} ${peer.name}");
  }

  @override
  void rtcSignalPeerUpdated(RtcPeer peer) {
    app.logI("peer updated: ${peer.id} ${peer.name}");
  }
}
