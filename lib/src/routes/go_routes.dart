// GENERATED CODE - DO NOT MODIFY BY HAND

// **************************************************************************
// RouterGenerator
// **************************************************************************

import 'package:go_router/go_router.dart';

import 'go_paths.dart';

// 🏷️ module =====> home
import '../modules/home/<USER>/home.controller.dart';
import '../modules/home/<USER>/home.view.dart';

/// APP 路由列表
final List<GoRoute> routes = [
  // 🏷️ module =====> home
  GoRoute(
    name: GoPaths.home,
    path: GoPaths.home,
    builder: (context, state) => HomeView(
      key: state.page<PERSON><PERSON>,
      binding: (key) => HomeController(key, state),
    ),
  ),
];
