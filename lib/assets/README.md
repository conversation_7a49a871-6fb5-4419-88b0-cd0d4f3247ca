# assets

存放项目资源, 管理方式, 请查阅 `app_foundation` 文档中 `assets` 模块

## 基本介绍

| 目录 | 用途与规范 |
| :-- | :-- |
| [fonts](./fonts/) | 存放三方字体文件, 每种字体请使用独立目录(字体名字)存放 |
| [images](./images/) | 存放 APP 本地图片资源, 按模块进行子文件目录划分, 然后再区分 2.0x 与 3.0x |
| [texts](./texts/) | 存放 APP 本地文本资源 |

注: 为了有效的控制安装包体积, 推荐使用 `webp` 格式的图片资源

1. 采用三方网站 [TinyPNG](https://tinify.cn/) 进行格式转换

2. 采用 `imagemagick` 命令工具集, 进行格式转换

   - **MacOS** 使用 `HomeBrew` 安装: `brew install imagemagick`

   - **Windows** 需进入 [官网](https://imagemagick.org/script/download.php#windows), 选择合适的安装包

[返回上级](../README.md)
