import 'dart:io';

import 'package:app_foundation/app_foundation.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:logger/logger.dart';

/// 请先移步至 ./src/routes/go_paths.dart 中, 定义路由信息
import './src/routes/go_routes.dart';

class MyHttpOverrides extends HttpOverrides {
  @override
  HttpClient createHttpClient(SecurityContext? context) {
    return super.createHttpClient(context)
      ..badCertificateCallback =
          (X509Certificate cert, String host, int port) => true;
  }
}

/// APP 页面路由器
final router = GoRouter(
  observers: [AppRouteObserver.share],
  navigatorKey: app.navigatorKey,
  routes: routes,
);

@AppDetector()
void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  HttpOverrides.global = MyHttpOverrides();
  runApp(
    AppBuilder(
      builder: (context, env) {
        return MaterialApp.router(
          scaffoldMessengerKey: app.messengerKey,
          routerConfig: router,
          theme: ThemeData(useMaterial3: true),
        );
      },
      onWillInit: () async {},
      onDidInit: (env) {
        app.setLog(Level.all);
        app.logI("App did init");
      },
    ),
  );
}
