#!/usr/bin/env bash

DOMAIN='https://localhost:3000'
TOKEN='eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpYXQiOjE1MTYyMzkwMjIsInVpZCI6IjMwMDAifQ.c7ENQyyYBpVQgbiAkCCi7Dr7khZVjffuQ1SRppgW-EU'
APP_ID='surgsmart'
PEER_ID='9999'
ROOM_ID='sdweqf'
MEDIA_FILE='./trailer.mp4'
AUDIO_PT=100
AUDIO_SSRC=1000
VIDEO_PT=101
VIDEO_SSRC=1001

response=$(curl -s -X 'POST' \
  "$DOMAIN/room/$ROOM_ID/join" \
  -H 'accept: application/json' \
  -H "Authorization: Bearer $TOKEN" \
  -H 'Content-Type: application/json' \
  -d '{
  "appId": "'$APP_ID'",
  "peerInfo": {
    "id": "'$PEER_ID'",
    "role": "broadcaster",
    "name": "主播",
    "avatar": "https://withai.com/avatar.png"
  }
}')

echo "join room: $response"

response=$(curl -s -X 'POST' \
  "$DOMAIN/room/$ROOM_ID/transport" \
  -H 'accept: application/json' \
  -H "Authorization: Bearer $TOKEN" \
  -H 'Content-Type: application/json' \
  -d '{
  "appId": "'$APP_ID'",
  "peerId": "'$PEER_ID'",
  "type": "plain",
  "enableProduce": true,
  "enableConsume": false
}')

read -r VIDEO_TRANSPORT_ID VIDEO_TRANSPORT_IP VIDEO_TRANSPORT_PORT VIDEO_TRANSPORT_RTCPPORT <<< \
  $(echo "$response" | jq -r '.id, .ip, .port, .rtcpPort')
echo "create video transport: $VIDEO_TRANSPORT_ID $VIDEO_TRANSPORT_IP $VIDEO_TRANSPORT_PORT $VIDEO_TRANSPORT_RTCPPORT"

response=$(curl -s -X 'POST' \
  "$DOMAIN/room/$ROOM_ID/transport" \
  -H 'accept: application/json' \
  -H "Authorization: Bearer $TOKEN" \
  -H 'Content-Type: application/json' \
  -d '{
  "appId": "'$APP_ID'",
  "peerId": "'$PEER_ID'",
  "type": "plain",
  "enableProduce": true,
  "enableConsume": false
}')

read -r AUDIO_TRANSPORT_ID AUDIO_TRANSPORT_IP AUDIO_TRANSPORT_PORT AUDIO_TRANSPORT_RTCPPORT <<< \
  $(echo "$response" | jq -r '.id, .ip, .port, .rtcpPort')
echo "create audio transport: $AUDIO_TRANSPORT_ID $AUDIO_TRANSPORT_IP $AUDIO_TRANSPORT_PORT $AUDIO_TRANSPORT_RTCPPORT"

response=$(curl -s -X 'POST' \
  "$DOMAIN/room/$ROOM_ID/transport/$VIDEO_TRANSPORT_ID/producer" \
  -H 'accept: application/json' \
  -H "Authorization: Bearer $TOKEN" \
  -H 'Content-Type: application/json' \
  -d '{
  "appId": "'$APP_ID'",
  "peerId": "'$PEER_ID'",
  "kind": "video",
  "rtpParameters": {
    "codecs": [
      {
        "mimeType": "video/VP8",
        "clockRate": 90000,
        "payloadType": '$VIDEO_PT'
      }
    ],
    "encodings": [
      {
        "ssrc": '$VIDEO_SSRC'
      }
    ]
  }
}')

echo "create video producer: $response"

response=$(curl -s -X 'POST' \
  "$DOMAIN/room/$ROOM_ID/transport/$AUDIO_TRANSPORT_ID/producer" \
  -H 'accept: application/json' \
  -H "Authorization: Bearer $TOKEN" \
  -H 'Content-Type: application/json' \
  -d '{
  "appId": "'$APP_ID'",
  "peerId": "'$PEER_ID'",
  "kind": "audio",
  "rtpParameters": {
    "codecs": [
      {
        "mimeType": "audio/opus",
        "clockRate": 48000,
        "payloadType": '$AUDIO_PT',
        "channels": 2,
        "parameters": {
          "sprop-stereo": 1
        }
      }
    ],
    "encodings": [
      {
        "ssrc": '$AUDIO_SSRC'
      }
    ]
  }
}')

echo "create audio producer: $response"

ffmpeg \
  -re \
  -v info \
  -stream_loop -1 \
  -i ${MEDIA_FILE} \
  -map 0:a:0 \
  -acodec libopus -ab 128k -ac 2 -ar 48000 \
  -map 0:v:0 \
  -pix_fmt yuv420p -c:v libvpx -b:v 6000k -deadline realtime -cpu-used 4 \
  -f tee \
  "[select=a:f=rtp:ssrc=${AUDIO_SSRC}:payload_type=${AUDIO_PT}]rtp://${AUDIO_TRANSPORT_IP}:${AUDIO_TRANSPORT_PORT}?rtcpport=${AUDIO_TRANSPORT_RTCPPORT}|[select=v:f=rtp:ssrc=${VIDEO_SSRC}:payload_type=${VIDEO_PT}]rtp://${VIDEO_TRANSPORT_IP}:${VIDEO_TRANSPORT_PORT}?rtcpport=${VIDEO_TRANSPORT_RTCPPORT}"

response=$(curl -s -X 'POST' \
  "$DOMAIN/room/$ROOM_ID/leave" \
  -H 'accept: */*' \
  -H "Authorization: Bearer $TOKEN" \
  -H 'Content-Type: application/json' \
  -d '{
  "appId": "'$APP_ID'",
  "peerId": "'$PEER_ID'"
}')

echo
echo "leave room"
